<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Medicine;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\Supplier;
use App\Models\Category;
use Carbon\Carbon;

class DashboardController extends Controller
{

    public function index()
    {
        // Get dashboard statistics
        $stats = [
            'total_medicines' => Medicine::active()->count(),
            'total_customers' => Customer::active()->count(),
            'total_suppliers' => Supplier::active()->count(),
            'total_categories' => Category::active()->count(),
            'low_stock_medicines' => Medicine::lowStock()->count(),
            'expiring_medicines' => Medicine::expiringSoon(30)->count(),
            'today_sales' => Sale::today()->completed()->count(),
            'today_revenue' => Sale::today()->completed()->sum('total_amount'),
            'this_month_sales' => Sale::thisMonth()->completed()->count(),
            'this_month_revenue' => Sale::thisMonth()->completed()->sum('total_amount'),
        ];

        // Recent sales
        $recent_sales = Sale::with(['customer', 'user'])
            ->latest()
            ->take(5)
            ->get();

        // Low stock medicines
        $low_stock_medicines = Medicine::with(['category', 'supplier'])
            ->lowStock()
            ->take(10)
            ->get();

        // Expiring medicines
        $expiring_medicines = Medicine::with(['category', 'supplier'])
            ->expiringSoon(30)
            ->take(10)
            ->get();

        return view('dashboard', compact('stats', 'recent_sales', 'low_stock_medicines', 'expiring_medicines'));
    }
}
