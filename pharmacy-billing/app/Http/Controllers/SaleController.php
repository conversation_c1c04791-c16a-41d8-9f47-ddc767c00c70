<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Medicine;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;

class SaleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,pharmacist,cashier');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Sale::with(['customer', 'user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('sale_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('sale_date', '<=', $request->date_to);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $sales = $query->latest('sale_date')->paginate(15);

        return view('sales.index', compact('sales'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::active()->get();
        $medicines = Medicine::active()->where('stock_quantity', '>', 0)->get();

        return view('sales.create', compact('customers', 'medicines'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.medicine_id' => 'required|exists:medicines,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'paid_amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,card,upi,cheque',
            'notes' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated, $request) {
            // Generate invoice number
            $sale = new Sale();
            $invoiceNumber = $sale->generateInvoiceNumber();

            // Calculate totals
            $subtotal = 0;
            foreach ($validated['items'] as $item) {
                $subtotal += ($item['quantity'] * $item['unit_price']) - ($item['discount_amount'] ?? 0);
            }

            $taxAmount = $validated['tax_amount'] ?? 0;
            $discountAmount = $validated['discount_amount'] ?? 0;
            $totalAmount = $subtotal + $taxAmount - $discountAmount;
            $changeAmount = $validated['paid_amount'] - $totalAmount;

            // Create sale
            $sale = Sale::create([
                'invoice_number' => $invoiceNumber,
                'customer_id' => $validated['customer_id'],
                'user_id' => auth()->id(),
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'paid_amount' => $validated['paid_amount'],
                'change_amount' => $changeAmount,
                'payment_method' => $validated['payment_method'],
                'status' => 'completed',
                'notes' => $validated['notes'],
                'sale_date' => now(),
            ]);

            // Create sale items and update stock
            foreach ($validated['items'] as $item) {
                $medicine = Medicine::find($item['medicine_id']);

                // Check stock availability
                if ($medicine->stock_quantity < $item['quantity']) {
                    throw new \Exception("Insufficient stock for {$medicine->name}");
                }

                // Create sale item
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'medicine_id' => $item['medicine_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => ($item['quantity'] * $item['unit_price']) - ($item['discount_amount'] ?? 0),
                    'discount_amount' => $item['discount_amount'] ?? 0,
                ]);

                // Update medicine stock
                $medicine->decrement('stock_quantity', $item['quantity']);
            }
        });

        return redirect()->route('sales.index')
            ->with('success', 'Sale completed successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Sale $sale)
    {
        $sale->load(['customer', 'user', 'saleItems.medicine']);

        return view('sales.show', compact('sale'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Sale $sale)
    {
        // Only allow editing of pending sales
        if ($sale->status !== 'pending') {
            return redirect()->route('sales.index')
                ->with('error', 'Only pending sales can be edited.');
        }

        $customers = Customer::active()->get();
        $medicines = Medicine::active()->where('stock_quantity', '>', 0)->get();
        $sale->load(['saleItems.medicine']);

        return view('sales.edit', compact('sale', 'customers', 'medicines'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Sale $sale)
    {
        // Only allow updating of pending sales
        if ($sale->status !== 'pending') {
            return redirect()->route('sales.index')
                ->with('error', 'Only pending sales can be updated.');
        }

        $validated = $request->validate([
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.medicine_id' => 'required|exists:medicines,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'tax_amount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'paid_amount' => 'required|numeric|min:0',
            'payment_method' => 'required|in:cash,card,upi,cheque',
            'notes' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated, $sale) {
            // Restore stock from old sale items
            foreach ($sale->saleItems as $oldItem) {
                $oldItem->medicine->increment('stock_quantity', $oldItem->quantity);
            }

            // Delete old sale items
            $sale->saleItems()->delete();

            // Calculate new totals
            $subtotal = 0;
            foreach ($validated['items'] as $item) {
                $subtotal += ($item['quantity'] * $item['unit_price']) - ($item['discount_amount'] ?? 0);
            }

            $taxAmount = $validated['tax_amount'] ?? 0;
            $discountAmount = $validated['discount_amount'] ?? 0;
            $totalAmount = $subtotal + $taxAmount - $discountAmount;
            $changeAmount = $validated['paid_amount'] - $totalAmount;

            // Update sale
            $sale->update([
                'customer_id' => $validated['customer_id'],
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'paid_amount' => $validated['paid_amount'],
                'change_amount' => $changeAmount,
                'payment_method' => $validated['payment_method'],
                'notes' => $validated['notes'],
            ]);

            // Create new sale items and update stock
            foreach ($validated['items'] as $item) {
                $medicine = Medicine::find($item['medicine_id']);

                // Check stock availability
                if ($medicine->stock_quantity < $item['quantity']) {
                    throw new \Exception("Insufficient stock for {$medicine->name}");
                }

                // Create sale item
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'medicine_id' => $item['medicine_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => ($item['quantity'] * $item['unit_price']) - ($item['discount_amount'] ?? 0),
                    'discount_amount' => $item['discount_amount'] ?? 0,
                ]);

                // Update medicine stock
                $medicine->decrement('stock_quantity', $item['quantity']);
            }
        });

        return redirect()->route('sales.index')
            ->with('success', 'Sale updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Sale $sale)
    {
        // Only allow deletion of pending or cancelled sales
        if (!in_array($sale->status, ['pending', 'cancelled'])) {
            return redirect()->route('sales.index')
                ->with('error', 'Only pending or cancelled sales can be deleted.');
        }

        DB::transaction(function () use ($sale) {
            // Restore stock if sale was pending
            if ($sale->status === 'pending') {
                foreach ($sale->saleItems as $item) {
                    $item->medicine->increment('stock_quantity', $item->quantity);
                }
            }

            $sale->delete();
        });

        return redirect()->route('sales.index')
            ->with('success', 'Sale deleted successfully.');
    }

    /**
     * Print invoice for the sale
     */
    public function printInvoice(Sale $sale)
    {
        $sale->load(['customer', 'user', 'saleItems.medicine']);

        return view('sales.invoice', compact('sale'));
    }
}
