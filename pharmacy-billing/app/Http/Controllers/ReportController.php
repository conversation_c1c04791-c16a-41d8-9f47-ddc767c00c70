<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Medicine;
use App\Models\Customer;
use App\Models\Supplier;
use Carbon\Carbon;

class ReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin,pharmacist');
    }

    public function index()
    {
        return view('reports.index');
    }

    public function sales(Request $request)
    {
        $query = Sale::with(['customer', 'user'])->completed();

        // Date filtering
        $dateFrom = $request->get('date_from', now()->startOfMonth()->toDateString());
        $dateTo = $request->get('date_to', now()->toDateString());

        $query->whereBetween('sale_date', [$dateFrom, $dateTo]);

        $sales = $query->latest('sale_date')->get();

        $totalSales = $sales->count();
        $totalRevenue = $sales->sum('total_amount');
        $averageSale = $totalSales > 0 ? $totalRevenue / $totalSales : 0;

        return view('reports.sales', compact('sales', 'totalSales', 'totalRevenue', 'averageSale', 'dateFrom', 'dateTo'));
    }

    public function inventory(Request $request)
    {
        $medicines = Medicine::with(['category', 'supplier'])->get();

        $lowStockMedicines = $medicines->filter(function($medicine) {
            return $medicine->isLowStock();
        });

        $expiringMedicines = $medicines->filter(function($medicine) {
            return $medicine->isExpiringSoon(30);
        });

        $totalValue = $medicines->sum(function($medicine) {
            return $medicine->stock_quantity * $medicine->purchase_price;
        });

        return view('reports.inventory', compact('medicines', 'lowStockMedicines', 'expiringMedicines', 'totalValue'));
    }

    public function customers(Request $request)
    {
        $customers = Customer::with(['sales' => function($query) {
            $query->completed();
        }])->get();

        $customers = $customers->map(function($customer) {
            $customer->total_purchases = $customer->sales->sum('total_amount');
            $customer->total_orders = $customer->sales->count();
            return $customer;
        })->sortByDesc('total_purchases');

        return view('reports.customers', compact('customers'));
    }

    public function suppliers(Request $request)
    {
        $suppliers = Supplier::with(['medicines', 'purchaseOrders'])->get();

        return view('reports.suppliers', compact('suppliers'));
    }
}
